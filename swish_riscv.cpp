// Tencent is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#include "swish_riscv.h"

#if __riscv_vector
#include <riscv_vector.h>
#endif // __riscv_vector

namespace ncnn {

int Swish_riscv::forward_inplace(Mat& bottom_top_blob, const Option& opt) const
{
    int w = bottom_top_blob.w;
    int h = bottom_top_blob.h;
    int d = bottom_top_blob.d;
    int channels = bottom_top_blob.c;
    int elempack = bottom_top_blob.elempack;
    int size = w * h * d * elempack;

    #pragma omp parallel for num_threads(opt.num_threads)
    for (int q = 0; q < channels; q++)
    {
        float* ptr = bottom_top_blob.channel(q);

#if __riscv_vector
        int n = size;
        while (n > 0)
        {
            size_t vl = __riscv_vsetvl_e32m8(n);

            vfloat32m8_t _p = __riscv_vle32_v_f32m8(ptr, vl);
            
            // Swish(x) = x * sigmoid(x)
            // Use approximation: sigmoid(x) ≈ 0.5 * (x / (1 + |x|)) + 0.5
            vfloat32m8_t _abs_p = __riscv_vfsgnjx_vv_f32m8(_p, _p, vl);
            vfloat32m8_t _one = __riscv_vfmv_v_f_f32m8(1.f, vl);
            vfloat32m8_t _half = __riscv_vfmv_v_f_f32m8(0.5f, vl);
            
            vfloat32m8_t _denom = __riscv_vfadd_vv_f32m8(_one, _abs_p, vl);
            vfloat32m8_t _ratio = __riscv_vfdiv_vv_f32m8(_p, _denom, vl);
            vfloat32m8_t _sigmoid = __riscv_vfmadd_vv_f32m8(_ratio, _half, _half, vl);
            
            // Swish = x * sigmoid(x)
            vfloat32m8_t _result = __riscv_vfmul_vv_f32m8(_p, _sigmoid, vl);
            
            __riscv_vse32_v_f32m8(ptr, _result, vl);

            ptr += vl;
            n -= vl;
        }
#else  // __riscv_vector
        for (int i = 0; i < size; i++)
        {
            float sigmoid_val = 1.f / (1.f + expf(-*ptr));
            *ptr = *ptr * sigmoid_val;
            ptr++;
        }
#endif // __riscv_vector
    }

    return 0;
}

} // namespace ncnn
