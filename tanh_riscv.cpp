// Tencent is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2019 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#include "tanh_riscv.h"

#if __riscv_vector
#include <riscv_vector.h>
#endif // __riscv_vector

namespace ncnn {

int TanH_riscv::forward_inplace(Mat& bottom_top_blob, const Option& opt) const
{
    int w = bottom_top_blob.w;
    int h = bottom_top_blob.h;
    int d = bottom_top_blob.d;
    int channels = bottom_top_blob.c;
    int elempack = bottom_top_blob.elempack;
    int size = w * h * d * elempack;

    #pragma omp parallel for num_threads(opt.num_threads)
    for (int q = 0; q < channels; q++)
    {
        float* ptr = bottom_top_blob.channel(q);

#if __riscv_vector
        int n = size;
        while (n > 0)
        {
            size_t vl = __riscv_vsetvl_e32m8(n);

            vfloat32m8_t _p = __riscv_vle32_v_f32m8(ptr, vl);
            
            // Clamp input to avoid overflow: tanh(x) ≈ 1 for x > 10, ≈ -1 for x < -10
            vfloat32m8_t _max_val = __riscv_vfmv_v_f_f32m8(10.f, vl);
            vfloat32m8_t _min_val = __riscv_vfmv_v_f_f32m8(-10.f, vl);
            _p = __riscv_vfmin_vv_f32m8(_p, _max_val, vl);
            _p = __riscv_vfmax_vv_f32m8(_p, _min_val, vl);
            
            // Use rational approximation: tanh(x) ≈ x * (27 + x²) / (27 + 9*x²)
            // This is accurate for |x| < 3
            vfloat32m8_t _x2 = __riscv_vfmul_vv_f32m8(_p, _p, vl);
            vfloat32m8_t _27 = __riscv_vfmv_v_f_f32m8(27.f, vl);
            vfloat32m8_t _9 = __riscv_vfmv_v_f_f32m8(9.f, vl);
            
            // Numerator: x * (27 + x²)
            vfloat32m8_t _num_inner = __riscv_vfadd_vv_f32m8(_27, _x2, vl);
            vfloat32m8_t _numerator = __riscv_vfmul_vv_f32m8(_p, _num_inner, vl);
            
            // Denominator: 27 + 9*x²
            vfloat32m8_t _9x2 = __riscv_vfmul_vv_f32m8(_9, _x2, vl);
            vfloat32m8_t _denominator = __riscv_vfadd_vv_f32m8(_27, _9x2, vl);
            
            // Result: numerator / denominator
            vfloat32m8_t _result = __riscv_vfdiv_vv_f32m8(_numerator, _denominator, vl);
            
            __riscv_vse32_v_f32m8(ptr, _result, vl);

            ptr += vl;
            n -= vl;
        }
#else  // __riscv_vector
        for (int i = 0; i < size; i++)
        {
            *ptr = tanhf(*ptr);
            ptr++;
        }
#endif // __riscv_vector
    }

    return 0;
}

} // namespace ncnn
