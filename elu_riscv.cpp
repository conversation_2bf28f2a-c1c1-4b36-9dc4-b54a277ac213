// Tencent is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#include "elu_riscv.h"

#if __riscv_vector
#include <riscv_vector.h>
#endif // __riscv_vector

namespace ncnn {

int ELU_riscv::forward_inplace(Mat& bottom_top_blob, const Option& opt) const
{
    int w = bottom_top_blob.w;
    int h = bottom_top_blob.h;
    int d = bottom_top_blob.d;
    int channels = bottom_top_blob.c;
    int elempack = bottom_top_blob.elempack;
    int size = w * h * d * elempack;

    #pragma omp parallel for num_threads(opt.num_threads)
    for (int q = 0; q < channels; q++)
    {
        float* ptr = bottom_top_blob.channel(q);

#if __riscv_vector
        int n = size;
        while (n > 0)
        {
            size_t vl = __riscv_vsetvl_e32m8(n);

            vfloat32m8_t _p = __riscv_vle32_v_f32m8(ptr, vl);
            vfloat32m8_t _zero = __riscv_vfmv_v_f_f32m8(0.f, vl);
            vfloat32m8_t _alpha = __riscv_vfmv_v_f_f32m8(alpha, vl);
            
            // Create mask for negative values
            vbool4_t _mask = __riscv_vmflt_vv_f32m8_b4(_p, _zero, vl);
            
            // For negative values: alpha * (exp(x) - 1)
            // Use approximation: exp(x) ≈ 1 + x + x²/2 for small x
            vfloat32m8_t _x2 = __riscv_vfmul_vv_f32m8(_p, _p, vl);
            vfloat32m8_t _half = __riscv_vfmv_v_f_f32m8(0.5f, vl);
            vfloat32m8_t _exp_approx = __riscv_vfmadd_vv_f32m8(_x2, _half, _p, vl);
            vfloat32m8_t _exp_approx_plus_one = __riscv_vfadd_vf_f32m8(_exp_approx, 1.f, vl);
            
            // For more accurate exp, clamp input to avoid overflow
            vfloat32m8_t _min_val = __riscv_vfmv_v_f_f32m8(-10.f, vl);
            vfloat32m8_t _clamped_p = __riscv_vfmax_vv_f32m8(_p, _min_val, vl);
            
            // Use better exp approximation for clamped values
            // exp(x) ≈ (1 + x/256)^256 for better accuracy
            vfloat32m8_t _inv256 = __riscv_vfmv_v_f_f32m8(1.0f/256.0f, vl);
            vfloat32m8_t _scaled = __riscv_vfmul_vv_f32m8(_clamped_p, _inv256, vl);
            vfloat32m8_t _one = __riscv_vfmv_v_f_f32m8(1.f, vl);
            vfloat32m8_t _base = __riscv_vfadd_vv_f32m8(_one, _scaled, vl);
            
            // Approximate (1+x/256)^256 by repeated squaring (8 times: 2^8 = 256)
            vfloat32m8_t _exp_val = _base;
            for (int i = 0; i < 8; i++) {
                _exp_val = __riscv_vfmul_vv_f32m8(_exp_val, _exp_val, vl);
            }
            
            vfloat32m8_t _neg_part = __riscv_vfmul_vv_f32m8(_alpha, __riscv_vfsub_vv_f32m8(_exp_val, _one, vl), vl);
            
            // Select between positive (unchanged) and negative (ELU) values
            vfloat32m8_t _result = __riscv_vmerge_vvm_f32m8(_p, _neg_part, _mask, vl);
            
            __riscv_vse32_v_f32m8(ptr, _result, vl);

            ptr += vl;
            n -= vl;
        }
#else  // __riscv_vector
        for (int i = 0; i < size; i++)
        {
            if (*ptr < 0.f)
                *ptr = alpha * (expf(*ptr) - 1.f);
            ptr++;
        }
#endif // __riscv_vector
    }

    return 0;
}

} // namespace ncnn
