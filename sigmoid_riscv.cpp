// Tencent is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#include "sigmoid_riscv.h"

#if __riscv_vector
#include <riscv_vector.h>
#include "rvv_mathfun.h"
#endif // __riscv_vector

namespace ncnn {

int Sigmoid_riscv::forward_inplace(Mat& bottom_top_blob, const Option& opt) const
{
    int w = bottom_top_blob.w;
    int h = bottom_top_blob.h;
    int d = bottom_top_blob.d;
    int channels = bottom_top_blob.c;
    int elempack = bottom_top_blob.elempack;
    int size = w * h * d * elempack;

    #pragma omp parallel for num_threads(opt.num_threads)
    for (int q = 0; q < channels; q++)
    {
        float* ptr = bottom_top_blob.channel(q);

#if __riscv_vector
        int n = size;
        while (n > 0)
        {
            size_t vl = __riscv_vsetvl_e32m8(n);

            vfloat32m8_t _p = __riscv_vle32_v_f32m8(ptr, vl);

            // Clamp input to avoid overflow: sigmoid(x) ≈ 1 for x > 20, ≈ 0 for x < -20
            vfloat32m8_t _max_val = __riscv_vfmv_v_f_f32m8(20.f, vl);
            vfloat32m8_t _min_val = __riscv_vfmv_v_f_f32m8(-20.f, vl);
            _p = __riscv_vfmin_vv_f32m8(_p, _max_val, vl);
            _p = __riscv_vfmax_vv_f32m8(_p, _min_val, vl);

            // Use polynomial approximation for exp(-x)
            // exp(x) ≈ 1 + x + x²/2 + x³/6 + x⁴/24 for small x
            vfloat32m8_t _neg_p = __riscv_vfrsub_vf_f32m8(_p, 0.f, vl);
            vfloat32m8_t _one = __riscv_vfmv_v_f_f32m8(1.f, vl);

            // For better accuracy, use the identity: sigmoid(x) = 0.5 * (1 + tanh(x/2))
            // Or use the approximation: sigmoid(x) ≈ 0.5 * (x / (1 + |x|)) + 0.5
            vfloat32m8_t _abs_p = __riscv_vfsgnjx_vv_f32m8(_p, _p, vl);
            vfloat32m8_t _denom = __riscv_vfadd_vv_f32m8(_one, _abs_p, vl);
            vfloat32m8_t _ratio = __riscv_vfdiv_vv_f32m8(_p, _denom, vl);
            vfloat32m8_t _half = __riscv_vfmv_v_f_f32m8(0.5f, vl);
            vfloat32m8_t _result = __riscv_vfmadd_vv_f32m8(_ratio, _half, _half, vl);

            __riscv_vse32_v_f32m8(ptr, _result, vl);

            ptr += vl;
            n -= vl;
        }
#else  // __riscv_vector
        for (int i = 0; i < size; i++)
        {
            *ptr = 1.f / (1.f + expf(-*ptr));
            ptr++;
        }
#endif // __riscv_vector
    }

    return 0;
}

} // namespace ncnn
