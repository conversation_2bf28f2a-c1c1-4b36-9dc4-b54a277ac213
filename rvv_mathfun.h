/* RISC-V Vector (RVV) implementation of sin, cos, exp and log

   Inspired by Intel Approximate Math library, and based on the
   corresponding algorithms of the cephes math library and SSE mathfun

   The default is to use RVV intrinsics with dynamic vector length.
   This provides better performance and flexibility compared to fixed-width SIMD.
*/

/* Copyright (C) 2007  <PERSON>ier

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.

  (this is the zlib license)
*/

#ifndef RVV_MATHFUN_H
#define RVV_MATHFUN_H

#include <riscv_vector.h>
#include "riscv_usability.h"

/* yes I know, the top of this file is quite ugly */

/* vfloat32m1_t is the basic vector type for RVV */
typedef vfloat32m1_t v4sf; // vector of float (rvv m1)
typedef vfloat32m2_t v8sf; // vector of float (rvv m2)
typedef vfloat32m4_t v16sf; // vector of float (rvv m4)
typedef vfloat32m8_t v32sf; // vector of float (rvv m8)

typedef vint32m1_t v4si; // vector of int (rvv m1)
typedef vint32m2_t v8si; // vector of int (rvv m2)
typedef vint32m4_t v16si; // vector of int (rvv m4)
typedef vint32m8_t v32si; // vector of int (rvv m8)

/* declare some RVV constants -- why can't I figure a better way to do that? */
#define _RVV_CONST(Name, Val) \
  static const float _rvv_##Name[1] = { (float)(Val) }

_RVV_CONST(1  , 1.0f);
_RVV_CONST(0p5, 0.5f);
/* the smallest non denormalized float number */
_RVV_CONST(min_norm_pos, 1.175494351e-38f);
_RVV_CONST(mant_mask, 0x7f800000);
_RVV_CONST(inv_mant_mask, ~0x7f800000);

_RVV_CONST(sign_mask, (float)0x80000000);
_RVV_CONST(inv_sign_mask, ~0x80000000);

_RVV_CONST(cephes_SQRTHF, 0.707106781186547524);
_RVV_CONST(cephes_log_p0, 7.0376836292E-2);
_RVV_CONST(cephes_log_p1, - 1.1514610310E-1);
_RVV_CONST(cephes_log_p2, 1.1676998740E-1);
_RVV_CONST(cephes_log_p3, - 1.2420140846E-1);
_RVV_CONST(cephes_log_p4, + 1.4249322787E-1);
_RVV_CONST(cephes_log_p5, - 1.6668057665E-1);
_RVV_CONST(cephes_log_p6, + 2.0000714765E-1);
_RVV_CONST(cephes_log_p7, - 2.4999993993E-1);
_RVV_CONST(cephes_log_p8, + 3.3333331174E-1);
_RVV_CONST(cephes_log_q1, -2.12194440e-4);
_RVV_CONST(cephes_log_q2, 0.693359375);

_RVV_CONST(exp_hi,	88.3762626647949f);
_RVV_CONST(exp_lo,	-88.3762626647949f);

_RVV_CONST(cephes_LOG2EF, 1.44269504088896341);
_RVV_CONST(cephes_exp_C1, 0.693359375);
_RVV_CONST(cephes_exp_C2, -2.12194440e-4);

_RVV_CONST(cephes_exp_p0, 1.9875691500E-4);
_RVV_CONST(cephes_exp_p1, 1.3981999507E-3);
_RVV_CONST(cephes_exp_p2, 8.3334519073E-3);
_RVV_CONST(cephes_exp_p3, 4.1665795894E-2);
_RVV_CONST(cephes_exp_p4, 1.6666665459E-1);
_RVV_CONST(cephes_exp_p5, 5.0000001201E-1);

_RVV_CONST(minus_cephes_DP1, -0.78515625);
_RVV_CONST(minus_cephes_DP2, -2.4187564849853515625e-4);
_RVV_CONST(minus_cephes_DP3, -3.77489497744594108e-8);
_RVV_CONST(sincof_p0, -1.9515295891E-4);
_RVV_CONST(sincof_p1,  8.3321608736E-3);
_RVV_CONST(sincof_p2, -1.6666654611E-1);
_RVV_CONST(coscof_p0,  2.443315711809948E-005);
_RVV_CONST(coscof_p1, -1.388731625493765E-003);
_RVV_CONST(coscof_p2,  4.166664568298827E-002);
_RVV_CONST(cephes_FOPI, 1.27323954473516); // 4 / M_PI

/* evaluation of 4 sines at once using RVV, maximum error is 2^-24 */
static inline vfloat32m1_t sin_ps_rvv(vfloat32m1_t x, size_t vl) {
  vfloat32m1_t xmm1, xmm2, xmm3, sign_bit, y;
  vint32m1_t emm0, emm2;
  
  sign_bit = x;
  /* take the absolute value */
  x = __riscv_vfsgnjx_vv_f32m1(x, x, vl);
  /* extract the sign bit (upper one) */
  vuint32m1_t sign_mask = __riscv_vmv_v_x_u32m1(0x80000000, vl);
  sign_bit = __riscv_vreinterpret_v_u32m1_f32m1(
    __riscv_vand_vv_u32m1(__riscv_vreinterpret_v_f32m1_u32m1(sign_bit), sign_mask, vl));

  /* scale by 4/Pi */
  y = __riscv_vfmul_vf_f32m1(x, *_rvv_cephes_FOPI, vl);

  /* store the integer part of y in mm0 */
  emm2 = __riscv_vfcvt_x_f_v_i32m1(y, vl);
  /* j=(j+1) & (~1) (see the cephes sources) */
  emm2 = __riscv_vadd_vx_i32m1(emm2, 1, vl);
  emm2 = __riscv_vand_vx_i32m1(emm2, ~1, vl);
  y = __riscv_vfcvt_f_x_v_f32m1(emm2, vl);

  /* get the swap sign flag */
  emm0 = __riscv_vand_vx_i32m1(emm2, 4, vl);
  vbool32_t swap_sign_mask = __riscv_vmsne_vx_i32m1_b32(emm0, 0, vl);
  vfloat32m1_t swap_sign_bit = __riscv_vfmerge_vfm_f32m1(
    __riscv_vfmv_v_f_f32m1(0.0f, vl), 
    __riscv_vreinterpret_v_u32m1_f32m1(sign_mask), 
    swap_sign_mask, vl);

  /* get the polynom selection mask 
     there is one polynom for 0 <= x <= Pi/4
     and another one for Pi/4<x<=Pi/2

     Both branches will be computed.
  */
  emm2 = __riscv_vand_vx_i32m1(emm2, 2, vl);
  vbool32_t poly_mask = __riscv_vmseq_vx_i32m1_b32(emm2, 0, vl);
  
  /* The magic pass: "Extended precision modular arithmetic" 
     x = ((x - y * DP1) - y * DP2) - y * DP3; */
  xmm1 = __riscv_vfmul_vf_f32m1(y, *_rvv_minus_cephes_DP1, vl);
  xmm2 = __riscv_vfmul_vf_f32m1(y, *_rvv_minus_cephes_DP2, vl);
  xmm3 = __riscv_vfmul_vf_f32m1(y, *_rvv_minus_cephes_DP3, vl);
  x = __riscv_vfadd_vv_f32m1(x, xmm1, vl);
  x = __riscv_vfadd_vv_f32m1(x, xmm2, vl);
  x = __riscv_vfadd_vv_f32m1(x, xmm3, vl);

  /* Evaluate the first polynom  (0 <= x <= Pi/4) */
  y = __riscv_vfmul_vf_f32m1(x, *_rvv_coscof_p0, vl);
  vfloat32m1_t z = __riscv_vfmul_vv_f32m1(x, x, vl);

  y = __riscv_vfadd_vf_f32m1(y, *_rvv_coscof_p1, vl);
  y = __riscv_vfmul_vv_f32m1(y, z, vl);
  y = __riscv_vfadd_vf_f32m1(y, *_rvv_coscof_p2, vl);
  y = __riscv_vfmul_vv_f32m1(y, z, vl);
  y = __riscv_vfmul_vv_f32m1(y, z, vl);
  vfloat32m1_t tmp = __riscv_vfmul_vf_f32m1(z, *_rvv_0p5, vl);
  y = __riscv_vfsub_vv_f32m1(y, tmp, vl);
  y = __riscv_vfadd_vf_f32m1(y, *_rvv_1, vl);

  /* Evaluate the second polynom  (Pi/4 <= x <= 0) */

  vfloat32m1_t y2 = __riscv_vfmul_vf_f32m1(x, *_rvv_sincof_p0, vl);
  y2 = __riscv_vfadd_vf_f32m1(y2, *_rvv_sincof_p1, vl);
  y2 = __riscv_vfmul_vv_f32m1(y2, z, vl);
  y2 = __riscv_vfadd_vf_f32m1(y2, *_rvv_sincof_p2, vl);
  y2 = __riscv_vfmul_vv_f32m1(y2, z, vl);
  y2 = __riscv_vfmul_vv_f32m1(y2, x, vl);
  y2 = __riscv_vfadd_vv_f32m1(y2, x, vl);

  /* select the correct result from the two polynoms */  
  y = __riscv_vmerge_vvm_f32m1(y2, y, poly_mask, vl);
  /* update the sign */
  sign_bit = __riscv_vfxor_vv_f32m1(sign_bit, swap_sign_bit, vl);
  y = __riscv_vfxor_vv_f32m1(y, sign_bit, vl);
  return y;
}

/* almost the same as sin_ps */
static inline vfloat32m1_t cos_ps_rvv(vfloat32m1_t x, size_t vl) {
  vfloat32m1_t xmm1, xmm2, xmm3, y;
  vint32m1_t emm0, emm2;
  
  /* take the absolute value */
  x = __riscv_vfsgnjx_vv_f32m1(x, x, vl);
  
  /* scale by 4/Pi */
  y = __riscv_vfmul_vf_f32m1(x, *_rvv_cephes_FOPI, vl);
  
  /* store the integer part of y in mm0 */
  emm2 = __riscv_vfcvt_x_f_v_i32m1(y, vl);
  /* j=(j+1) & (~1) (see the cephes sources) */
  emm2 = __riscv_vadd_vx_i32m1(emm2, 1, vl);
  emm2 = __riscv_vand_vx_i32m1(emm2, ~1, vl);
  y = __riscv_vfcvt_f_x_v_f32m1(emm2, vl);
  
  emm2 = __riscv_vsub_vx_i32m1(emm2, 2, vl);
  
  /* get the swap sign flag */
  emm0 = __riscv_vand_vx_i32m1(emm2, 4, vl);
  vbool32_t swap_sign_mask = __riscv_vmsne_vx_i32m1_b32(emm0, 0, vl);
  vuint32m1_t sign_mask = __riscv_vmv_v_x_u32m1(0x80000000, vl);
  vfloat32m1_t swap_sign_bit = __riscv_vfmerge_vfm_f32m1(
    __riscv_vfmv_v_f_f32m1(0.0f, vl), 
    __riscv_vreinterpret_v_u32m1_f32m1(sign_mask), 
    swap_sign_mask, vl);

  /* get the polynom selection mask */
  emm2 = __riscv_vand_vx_i32m1(emm2, 2, vl);
  vbool32_t poly_mask = __riscv_vmseq_vx_i32m1_b32(emm2, 0, vl);
  
  /* The magic pass: "Extended precision modular arithmetic" 
     x = ((x - y * DP1) - y * DP2) - y * DP3; */
  xmm1 = __riscv_vfmul_vf_f32m1(y, *_rvv_minus_cephes_DP1, vl);
  xmm2 = __riscv_vfmul_vf_f32m1(y, *_rvv_minus_cephes_DP2, vl);
  xmm3 = __riscv_vfmul_vf_f32m1(y, *_rvv_minus_cephes_DP3, vl);
  x = __riscv_vfadd_vv_f32m1(x, xmm1, vl);
  x = __riscv_vfadd_vv_f32m1(x, xmm2, vl);
  x = __riscv_vfadd_vv_f32m1(x, xmm3, vl);
  
  /* Evaluate the first polynom  (0 <= x <= Pi/4) */
  y = __riscv_vfmul_vf_f32m1(x, *_rvv_coscof_p0, vl);
  vfloat32m1_t z = __riscv_vfmul_vv_f32m1(x, x, vl);

  y = __riscv_vfadd_vf_f32m1(y, *_rvv_coscof_p1, vl);
  y = __riscv_vfmul_vv_f32m1(y, z, vl);
  y = __riscv_vfadd_vf_f32m1(y, *_rvv_coscof_p2, vl);
  y = __riscv_vfmul_vv_f32m1(y, z, vl);
  y = __riscv_vfmul_vv_f32m1(y, z, vl);
  vfloat32m1_t tmp = __riscv_vfmul_vf_f32m1(z, *_rvv_0p5, vl);
  y = __riscv_vfsub_vv_f32m1(y, tmp, vl);
  y = __riscv_vfadd_vf_f32m1(y, *_rvv_1, vl);

  /* Evaluate the second polynom  (Pi/4 <= x <= 0) */
  vfloat32m1_t y2 = __riscv_vfmul_vf_f32m1(x, *_rvv_sincof_p0, vl);
  y2 = __riscv_vfadd_vf_f32m1(y2, *_rvv_sincof_p1, vl);
  y2 = __riscv_vfmul_vv_f32m1(y2, z, vl);
  y2 = __riscv_vfadd_vf_f32m1(y2, *_rvv_sincof_p2, vl);
  y2 = __riscv_vfmul_vv_f32m1(y2, z, vl);
  y2 = __riscv_vfmul_vv_f32m1(y2, x, vl);
  y2 = __riscv_vfadd_vv_f32m1(y2, x, vl);

  /* select the correct result from the two polynoms */  
  y = __riscv_vmerge_vvm_f32m1(y2, y, poly_mask, vl);
  /* update the sign */
  y = __riscv_vfxor_vv_f32m1(y, swap_sign_bit, vl);

  return y;
}

#endif // RVV_MATHFUN_H
