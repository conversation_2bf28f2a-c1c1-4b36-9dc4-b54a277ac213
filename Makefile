# Makefile for RISC-V Vector NCNN Operators
# Targets Xuantie C910 RISC-V processor with Vector, ZFH, and ZVFH extensions

# Compiler settings
CXX = riscv64-unknown-linux-gnu-g++
CXXFLAGS = -std=c++11 -O2 -Wall -Wextra

# RISC-V specific flags
RISCV_ARCH = rv64gcv_zfh_zvfh
RISCV_FLAGS = -march=$(RISCV_ARCH) -mrvv-vector-bits=zvl

# Preprocessor definitions
DEFINES = -D__riscv_vector=1 -DNCNN_ZFH=1

# Include directories (adjust paths as needed)
INCLUDES = -I. -I../include

# All flags combined
ALL_CXXFLAGS = $(CXXFLAGS) $(RISCV_FLAGS) $(DEFINES) $(INCLUDES)

# Source files
SOURCES = bias_riscv.cpp \
          binaryop_riscv.cpp \
          relu_riscv.cpp \
          scale_riscv.cpp \
          sigmoid_riscv.cpp \
          tanh_riscv.cpp \
          elu_riscv.cpp \
          swish_riscv.cpp \
          batchnorm_riscv.cpp \
          clip_riscv.cpp \
          pooling_riscv.cpp

# Object files
OBJECTS = $(SOURCES:.cpp=.o)

# Header files
HEADERS = bias_riscv.h \
          binaryop_riscv.h \
          relu_riscv.h \
          scale_riscv.h \
          sigmoid_riscv.h \
          tanh_riscv.h \
          elu_riscv.h \
          swish_riscv.h \
          batchnorm_riscv.h \
          clip_riscv.h \
          pooling_riscv.h \
          riscv_usability.h \
          rvv_mathfun.h

# Test executable
TEST_TARGET = test_riscv_operators
TEST_SOURCE = test_riscv_operators.cpp

# Library target
LIB_TARGET = libncnn_riscv.a

# Default target
all: $(LIB_TARGET) $(TEST_TARGET)

# Build library
$(LIB_TARGET): $(OBJECTS)
	ar rcs $@ $^
	@echo "Built RISC-V library: $@"

# Build test executable
$(TEST_TARGET): $(TEST_SOURCE) $(HEADERS)
	$(CXX) $(ALL_CXXFLAGS) -o $@ $(TEST_SOURCE)
	@echo "Built test executable: $@"

# Compile source files
%.o: %.cpp $(HEADERS)
	$(CXX) $(ALL_CXXFLAGS) -c $< -o $@

# Clean build artifacts
clean:
	rm -f $(OBJECTS) $(LIB_TARGET) $(TEST_TARGET)
	@echo "Cleaned build artifacts"

# Install headers and library (adjust paths as needed)
install: $(LIB_TARGET)
	mkdir -p $(DESTDIR)/usr/local/lib
	mkdir -p $(DESTDIR)/usr/local/include/ncnn/riscv
	cp $(LIB_TARGET) $(DESTDIR)/usr/local/lib/
	cp $(HEADERS) $(DESTDIR)/usr/local/include/ncnn/riscv/
	@echo "Installed library and headers"

# Run tests
test: $(TEST_TARGET)
	./$(TEST_TARGET)

# Check for RISC-V vector support
check-rvv:
	@echo "Checking RISC-V Vector support..."
	@$(CXX) $(RISCV_FLAGS) -dM -E - < /dev/null | grep -i vector || echo "RVV not detected"

# Generate assembly output for inspection
asm: $(SOURCES)
	$(CXX) $(ALL_CXXFLAGS) -S -fverbose-asm $(SOURCES)
	@echo "Generated assembly files (.s)"

# Performance benchmark (requires actual RISC-V hardware)
benchmark: $(TEST_TARGET)
	@echo "Running performance benchmark..."
	time ./$(TEST_TARGET)

# Debug build
debug: CXXFLAGS += -g -DDEBUG
debug: $(LIB_TARGET) $(TEST_TARGET)

# Profile build
profile: CXXFLAGS += -pg
profile: $(LIB_TARGET) $(TEST_TARGET)

# Documentation
docs:
	@echo "Documentation available in RISC-V_MIGRATION_README.md"
	@echo "Key files migrated:"
	@echo "  - bias_x86.* -> bias_riscv.*"
	@echo "  - binaryop_x86.* -> binaryop_riscv.*"
	@echo "  - relu_x86.* -> relu_riscv.*"
	@echo "  - scale_x86.* -> scale_riscv.*"
	@echo "  - x86_usability.h -> riscv_usability.h"
	@echo "  - sse_mathfun.h + avx_mathfun.h -> rvv_mathfun.h"

# Show compiler and target information
info:
	@echo "Compiler: $(CXX)"
	@echo "Target Architecture: $(RISCV_ARCH)"
	@echo "Compiler Flags: $(ALL_CXXFLAGS)"
	@echo "Sources: $(SOURCES)"
	@echo "Headers: $(HEADERS)"

# Validate code style (requires clang-format)
format:
	clang-format -i $(SOURCES) $(HEADERS) $(TEST_SOURCE)
	@echo "Formatted source files"

# Static analysis (requires cppcheck)
analyze:
	cppcheck --enable=all --std=c++11 $(SOURCES) $(HEADERS)

# Help target
help:
	@echo "Available targets:"
	@echo "  all        - Build library and test executable (default)"
	@echo "  $(LIB_TARGET) - Build RISC-V library"
	@echo "  $(TEST_TARGET) - Build test executable"
	@echo "  clean      - Remove build artifacts"
	@echo "  install    - Install library and headers"
	@echo "  test       - Run test executable"
	@echo "  check-rvv  - Check for RISC-V Vector support"
	@echo "  asm        - Generate assembly output"
	@echo "  benchmark  - Run performance benchmark"
	@echo "  debug      - Build with debug symbols"
	@echo "  profile    - Build with profiling support"
	@echo "  docs       - Show documentation info"
	@echo "  info       - Show compiler and target info"
	@echo "  format     - Format source code"
	@echo "  analyze    - Run static analysis"
	@echo "  help       - Show this help message"

.PHONY: all clean install test check-rvv asm benchmark debug profile docs info format analyze help
