# RISC-V Vector (RVV) Migration for NCNN Neural Network Operators

This document describes the migration of x86 SIMD intrinsics (SSE2, AVX, AVX2, AVX-512F) to RISC-V Vector (RVV) intrinsics for the NCNN neural network framework.

## Overview

The migration targets the Xuantie C910 RISC-V processor with Vector, ZFH (half-precision float), and ZVFH (vector half-precision float) extensions.

## Migrated Files

### Core Operators
1. **Bias Operator**
   - `bias_x86.h` → `bias_riscv.h`
   - `bias_x86.cpp` → `bias_riscv.cpp`

2. **Binary Operations**
   - `binaryop_x86.h` → `binaryop_riscv.h`
   - `binaryop_x86.cpp` → `binaryop_riscv.cpp`

3. **ReLU Activation**
   - `relu_x86.h` → `relu_riscv.h`
   - `relu_x86.cpp` → `relu_riscv.cpp`

4. **Scale Operator**
   - `scale_x86.h` → `scale_riscv.h`
   - `scale_x86.cpp` → `scale_riscv.cpp`

### Utility Headers
1. **RISC-V Utilities**
   - `x86_usability.h` → `riscv_usability.h`

2. **Math Functions**
   - `sse_mathfun.h` + `avx_mathfun.h` → `rvv_mathfun.h`

## Key Migration Strategies

### 1. Intrinsic Mapping

| x86 Intrinsic | RISC-V Vector Equivalent | Notes |
|---------------|--------------------------|-------|
| `__m128` | `vfloat32m1_t` | 128-bit float vector |
| `__m256` | `vfloat32m2_t` or `vfloat32m4_t` | 256-bit float vector |
| `__m512` | `vfloat32m4_t` or `vfloat32m8_t` | 512-bit float vector |
| `_mm_loadu_ps()` | `__riscv_vle32_v_f32m1()` | Load unaligned float |
| `_mm_storeu_ps()` | `__riscv_vse32_v_f32m1()` | Store unaligned float |
| `_mm_add_ps()` | `__riscv_vfadd_vv_f32m1()` | Vector addition |
| `_mm_mul_ps()` | `__riscv_vfmul_vv_f32m1()` | Vector multiplication |
| `_mm_max_ps()` | `__riscv_vfmax_vv_f32m1()` | Vector maximum |
| `_mm_set1_ps()` | `__riscv_vfmv_v_f_f32m1()` | Broadcast scalar |

### 2. Vector Length Handling

RVV uses dynamic vector length instead of fixed-width vectors:

```cpp
// x86 fixed-width approach
for (; i + 7 < size; i += 8) {
    __m256 _p = _mm256_loadu_ps(ptr);
    __m256 _res = _mm256_add_ps(_p, _bias);
    _mm256_storeu_ps(ptr, _res);
    ptr += 8;
}

// RVV dynamic vector length approach
int n = size;
while (n > 0) {
    size_t vl = __riscv_vsetvl_e32m8(n);
    vfloat32m8_t _p = __riscv_vle32_v_f32m8(ptr, vl);
    vfloat32m8_t _res = __riscv_vfadd_vf_f32m8(_p, bias, vl);
    __riscv_vse32_v_f32m8(ptr, _res, vl);
    ptr += vl;
    n -= vl;
}
```

### 3. LMUL (Length Multiplier) Selection

- `m1`: Basic vector length, good for simple operations
- `m2`: 2x vector length, better throughput for independent operations
- `m4`: 4x vector length, maximum throughput with moderate register pressure
- `m8`: 8x vector length, maximum throughput but high register pressure

### 4. Half-Precision (FP16) Support

RVV provides native FP16 support with conversion intrinsics:

```cpp
// Convert FP16 to FP32, process, then convert back
vfloat16m4_t _p = __riscv_vle16_v_f16m4(ptr, vl);
vfloat32m8_t _p_fp32 = __riscv_vfwcvt_f_f_v_f32m8(_p, vl);
vfloat32m8_t _res = __riscv_vfadd_vf_f32m8(_p_fp32, bias, vl);
vfloat16m4_t _res_fp16 = __riscv_vfncvt_f_f_w_f16m4(_res, vl);
__riscv_vse16_v_f16m4(ptr, _res_fp16, vl);
```

## Performance Optimizations

### 1. Strip-Mining Pattern
All loops use the strip-mining pattern for optimal performance with variable vector lengths.

### 2. Register Pressure Management
- Use appropriate LMUL values based on operation complexity
- Prefer m8 for simple operations, m4 for complex operations

### 3. Predication Support
RVV's predication is used for conditional operations (e.g., ReLU):

```cpp
vbool4_t _mask = __riscv_vmflt_vv_f32m8_b4(_p, _zero, vl);
vfloat32m8_t _res = __riscv_vmerge_vvm_f32m8(_p, _neg_part, _mask, vl);
```

### 4. Fused Operations
Use fused multiply-add when available:

```cpp
// scale * x + bias
vfloat32m8_t _res = __riscv_vfmadd_vf_f32m8(_p, scale, bias, vl);
```

## Compilation Requirements

### Compiler Flags
```bash
-march=rv64gcv_zfh_zvfh  # Enable vector, zfh, and zvfh extensions
-mrvv-vector-bits=zvl     # Use scalable vector length
```

### Preprocessor Definitions
```cpp
#define __riscv_vector 1
#define NCNN_ZFH 1  // Enable half-precision support
```

## Testing and Validation

### Functional Testing
1. Compare outputs with reference x86 implementations
2. Test edge cases (small vectors, unaligned data)
3. Validate FP16 precision

### Performance Testing
1. Benchmark against scalar implementations
2. Compare with x86 SIMD performance
3. Profile register usage and memory bandwidth

## Known Limitations and Differences

### 1. Vector Length Differences
- RVV vector length is implementation-defined
- May not match x86 fixed widths exactly
- Use dynamic algorithms instead of fixed unrolling

### 2. Precision Differences
- RVV may have slightly different rounding behavior
- FP16 conversions may introduce small precision differences

### 3. Memory Alignment
- RVV is more flexible with alignment requirements
- Some x86 alignment optimizations may not be necessary

## Future Extensions

### Additional Operators to Migrate
1. Convolution operators
2. Pooling operators
3. Normalization operators (BatchNorm, LayerNorm)
4. Activation functions (Sigmoid, Tanh, Softmax)

### Advanced Optimizations
1. Loop fusion opportunities
2. Memory access pattern optimization
3. Multi-threading with RVV
4. Mixed-precision optimizations

## References

1. RISC-V Vector Extension Specification v1.0
2. NCNN Framework Documentation
3. Xuantie C910 Processor Manual
4. RVV Intrinsics Reference Guide
