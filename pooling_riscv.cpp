// Tencent is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#include "pooling_riscv.h"

#if __riscv_vector
#include <riscv_vector.h>
#include "riscv_usability.h"
#endif // __riscv_vector

namespace ncnn {

int Pooling_riscv::forward(const Mat& bottom_blob, Mat& top_blob, const Option& opt) const
{
    if (pooling_type == PoolMethod_MAX)
    {
        return pooling_max(bottom_blob, top_blob, opt);
    }
    else if (pooling_type == PoolMethod_AVE)
    {
        return pooling_ave(bottom_blob, top_blob, opt);
    }

    return 0;
}

int Pooling_riscv::pooling_max(const Mat& bottom_blob, Mat& top_blob, const Option& opt) const
{
    int w = bottom_blob.w;
    int h = bottom_blob.h;
    int channels = bottom_blob.c;
    int elempack = bottom_blob.elempack;

    int outw = top_blob.w;
    int outh = top_blob.h;

    #pragma omp parallel for num_threads(opt.num_threads)
    for (int q = 0; q < channels; q++)
    {
        const float* ptr = bottom_blob.channel(q);
        float* outptr = top_blob.channel(q);

        for (int i = 0; i < outh; i++)
        {
            for (int j = 0; j < outw; j++)
            {
                int hstart = i * stride_h - pad_top;
                int wstart = j * stride_w - pad_left;
                int hend = std::min(hstart + kernel_h, h);
                int wend = std::min(wstart + kernel_w, w);
                hstart = std::max(hstart, 0);
                wstart = std::max(wstart, 0);

                float max_val = -FLT_MAX;

#if __riscv_vector
                if (elempack == 1)
                {
                    for (int y = hstart; y < hend; y++)
                    {
                        const float* row_ptr = ptr + y * w;
                        int x = wstart;
                        int remain = wend - wstart;
                        
                        vfloat32m8_t _max = __riscv_vfmv_v_f_f32m8(-FLT_MAX, __riscv_vsetvl_e32m8(remain));
                        
                        while (remain > 0)
                        {
                            size_t vl = __riscv_vsetvl_e32m8(remain);
                            vfloat32m8_t _val = __riscv_vle32_v_f32m8(row_ptr + x, vl);
                            _max = __riscv_vfmax_vv_f32m8(_max, _val, vl);
                            x += vl;
                            remain -= vl;
                        }
                        
                        // Reduce vector to scalar
                        float row_max = vfredmax_vs_f32m8_f32(_max, __riscv_vsetvl_e32m8(wend - wstart));
                        max_val = std::max(max_val, row_max);
                    }
                }
                else
#endif // __riscv_vector
                {
                    // Scalar fallback or packed elements
                    for (int y = hstart; y < hend; y++)
                    {
                        for (int x = wstart; x < wend; x++)
                        {
                            for (int k = 0; k < elempack; k++)
                            {
                                float val = ptr[(y * w + x) * elempack + k];
                                max_val = std::max(max_val, val);
                            }
                        }
                    }
                }

                if (elempack == 1)
                {
                    outptr[i * outw + j] = max_val;
                }
                else
                {
                    for (int k = 0; k < elempack; k++)
                    {
                        outptr[(i * outw + j) * elempack + k] = max_val;
                    }
                }
            }
        }
    }

    return 0;
}

int Pooling_riscv::pooling_ave(const Mat& bottom_blob, Mat& top_blob, const Option& opt) const
{
    int w = bottom_blob.w;
    int h = bottom_blob.h;
    int channels = bottom_blob.c;
    int elempack = bottom_blob.elempack;

    int outw = top_blob.w;
    int outh = top_blob.h;

    #pragma omp parallel for num_threads(opt.num_threads)
    for (int q = 0; q < channels; q++)
    {
        const float* ptr = bottom_blob.channel(q);
        float* outptr = top_blob.channel(q);

        for (int i = 0; i < outh; i++)
        {
            for (int j = 0; j < outw; j++)
            {
                int hstart = i * stride_h - pad_top;
                int wstart = j * stride_w - pad_left;
                int hend = std::min(hstart + kernel_h, h);
                int wend = std::min(wstart + kernel_w, w);
                hstart = std::max(hstart, 0);
                wstart = std::max(wstart, 0);

                float sum_val = 0.f;
                int area = (hend - hstart) * (wend - wstart);

#if __riscv_vector
                if (elempack == 1)
                {
                    for (int y = hstart; y < hend; y++)
                    {
                        const float* row_ptr = ptr + y * w;
                        int x = wstart;
                        int remain = wend - wstart;
                        
                        vfloat32m8_t _sum = __riscv_vfmv_v_f_f32m8(0.f, __riscv_vsetvl_e32m8(remain));
                        
                        while (remain > 0)
                        {
                            size_t vl = __riscv_vsetvl_e32m8(remain);
                            vfloat32m8_t _val = __riscv_vle32_v_f32m8(row_ptr + x, vl);
                            _sum = __riscv_vfadd_vv_f32m8(_sum, _val, vl);
                            x += vl;
                            remain -= vl;
                        }
                        
                        // Reduce vector to scalar
                        float row_sum = vfredsum_vs_f32m8_f32(_sum, __riscv_vsetvl_e32m8(wend - wstart));
                        sum_val += row_sum;
                    }
                }
                else
#endif // __riscv_vector
                {
                    // Scalar fallback or packed elements
                    for (int y = hstart; y < hend; y++)
                    {
                        for (int x = wstart; x < wend; x++)
                        {
                            for (int k = 0; k < elempack; k++)
                            {
                                sum_val += ptr[(y * w + x) * elempack + k];
                            }
                        }
                    }
                }

                float avg_val = sum_val / area;

                if (elempack == 1)
                {
                    outptr[i * outw + j] = avg_val;
                }
                else
                {
                    for (int k = 0; k < elempack; k++)
                    {
                        outptr[(i * outw + j) * elempack + k] = avg_val;
                    }
                }
            }
        }
    }

    return 0;
}

} // namespace ncnn
