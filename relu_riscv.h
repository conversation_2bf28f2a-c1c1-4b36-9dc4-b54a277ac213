// Ten<PERSON> is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#ifndef LAYER_RELU_RISCV_H
#define LAYER_RELU_RISCV_H

#include "relu.h"

namespace ncnn {

class ReLU_riscv : public ReLU
{
public:
    ReLU_riscv();

    virtual int forward_inplace(Mat& bottom_top_blob, const Option& opt) const;

#if NCNN_ZFH
    virtual int forward_inplace_fp16s(Mat& bottom_top_blob, const Option& opt) const;
#endif
};

} // namespace ncnn

#endif // LAYER_RELU_RISCV_H
