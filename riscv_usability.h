// <PERSON><PERSON> is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#ifndef RISCV_USABILITY_H
#define RISCV_USABILITY_H

#include <stdint.h>
#if __riscv_vector
#include <riscv_vector.h>
#endif // __riscv_vector

#define NCNN_FORCEINLINE inline __attribute__((always_inline))

namespace ncnn {

#if __riscv_vector

// Helper function to convert float to int8 with rounding
static NCNN_FORCEINLINE int32_t float2int8_rvv(const vfloat32m1_t& _v0, size_t vl)
{
    // Round to nearest integer
    vfloat32m1_t _p5 = __riscv_vfmv_v_f_f32m1(0.5f, vl);
    vfloat32m1_t _n5 = __riscv_vfmv_v_f_f32m1(-0.5f, vl);
    
    // Create sign mask and apply rounding
    vbool32_t _pos_mask = __riscv_vmfge_vf_f32m1_b32(_v0, 0.0f, vl);
    vfloat32m1_t _round_val = __riscv_vmerge_vvm_f32m1(_n5, _p5, _pos_mask, vl);
    vfloat32m1_t _v0_adj = __riscv_vfadd_vv_f32m1(_v0, _round_val, vl);
    
    // Convert to int32
    vint32m1_t _v0_i = __riscv_vfcvt_x_f_v_i32m1(_v0_adj, vl);
    
    // Clamp to int8 range [-127, 127]
    vint32m1_t _v0_clamped = __riscv_vmax_vx_i32m1(_v0_i, -127, vl);
    _v0_clamped = __riscv_vmin_vx_i32m1(_v0_clamped, 127, vl);
    
    // Extract first element (for compatibility with x86 version)
    return __riscv_vmv_x_s_i32m1_i32(_v0_clamped);
}

// Helper function to combine two vectors (similar to x86's combine functions)
static NCNN_FORCEINLINE vfloat32m2_t combine2x1_ps(vfloat32m1_t a, vfloat32m1_t b, size_t vl)
{
    vfloat32m2_t result = __riscv_vundefined_f32m2();
    result = __riscv_vset_v_f32m1_f32m2(result, 0, a);
    result = __riscv_vset_v_f32m1_f32m2(result, 1, b);
    return result;
}

// Vector reduction sum for float32
static NCNN_FORCEINLINE float vfredsum_vs_f32m8_f32(vfloat32m8_t vec, size_t vl)
{
    vfloat32m1_t scalar_zero = __riscv_vfmv_v_f_f32m1(0.0f, 1);
    vfloat32m1_t sum = __riscv_vfredsum_vs_f32m8_f32m1(vec, scalar_zero, vl);
    return __riscv_vfmv_f_s_f32m1_f32(sum);
}

// Vector reduction sum for float32m4
static NCNN_FORCEINLINE float vfredsum_vs_f32m4_f32(vfloat32m4_t vec, size_t vl)
{
    vfloat32m1_t scalar_zero = __riscv_vfmv_v_f_f32m1(0.0f, 1);
    vfloat32m1_t sum = __riscv_vfredsum_vs_f32m4_f32m1(vec, scalar_zero, vl);
    return __riscv_vfmv_f_s_f32m1_f32(sum);
}

// Vector reduction sum for float32m2
static NCNN_FORCEINLINE float vfredsum_vs_f32m2_f32(vfloat32m2_t vec, size_t vl)
{
    vfloat32m1_t scalar_zero = __riscv_vfmv_v_f_f32m1(0.0f, 1);
    vfloat32m1_t sum = __riscv_vfredsum_vs_f32m2_f32m1(vec, scalar_zero, vl);
    return __riscv_vfmv_f_s_f32m1_f32(sum);
}

// Vector reduction sum for float32m1
static NCNN_FORCEINLINE float vfredsum_vs_f32m1_f32(vfloat32m1_t vec, size_t vl)
{
    vfloat32m1_t scalar_zero = __riscv_vfmv_v_f_f32m1(0.0f, 1);
    vfloat32m1_t sum = __riscv_vfredsum_vs_f32m1_f32m1(vec, scalar_zero, vl);
    return __riscv_vfmv_f_s_f32m1_f32(sum);
}

// Helper for packing operations
static NCNN_FORCEINLINE vuint16m4_t float2bfloat16_rvv(vfloat32m8_t _v, size_t vl)
{
    // Convert float32 to bfloat16 by truncating mantissa
    vuint32m8_t _v_u32 = __riscv_vreinterpret_v_f32m8_u32m8(_v);
    vuint32m8_t _v_shifted = __riscv_vsrl_vx_u32m8(_v_u32, 16, vl);
    vuint16m4_t _v_bf16 = __riscv_vncvt_x_x_w_u16m4(_v_shifted, vl);
    return _v_bf16;
}

// Helper for unpacking operations  
static NCNN_FORCEINLINE vfloat32m8_t bfloat162float_rvv(vuint16m4_t _v, size_t vl)
{
    // Convert bfloat16 to float32 by zero-padding mantissa
    vuint32m8_t _v_u32 = __riscv_vwcvtu_x_x_v_u32m8(_v, vl);
    vuint32m8_t _v_shifted = __riscv_vsll_vx_u32m8(_v_u32, 16, vl);
    vfloat32m8_t _v_f32 = __riscv_vreinterpret_v_u32m8_f32m8(_v_shifted);
    return _v_f32;
}

#endif // __riscv_vector

} // namespace ncnn

#endif // RISCV_USABILITY_H
