#!/bin/bash

# Script to create remaining RISC-V operators
# This creates basic template files for all remaining operators

# List of remaining operators to create
OPERATORS=(
    "gelu"
    "mish" 
    "selu"
    "hardsigmoid"
    "hardswish"
    "softmax"
    "layernorm"
    "groupnorm"
    "rmsnorm"
    "eltwise"
    "unaryop"
    "prelu"
    "bnll"
    "packing"
    "padding"
    "crop"
    "concat"
    "slice"
    "reshape"
    "flatten"
    "shufflechannel"
    "interp"
    "quantize"
    "dequantize"
    "requantize"
    "lrn"
    "dropout"
)

# Function to create header file
create_header() {
    local op_name=$1
    local class_name=$(echo $op_name | sed 's/\b\w/\U&/g')  # Capitalize first letter
    local header_guard="LAYER_${op_name^^}_RISCV_H"
    
    cat > "${op_name}_riscv.h" << EOF
// Tencent is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#ifndef ${header_guard}
#define ${header_guard}

#include "${op_name}.h"

namespace ncnn {

class ${class_name}_riscv : public ${class_name}
{
public:
    virtual int forward_inplace(Mat& bottom_top_blob, const Option& opt) const;
};

} // namespace ncnn

#endif // ${header_guard}
EOF
}

# Function to create implementation file
create_implementation() {
    local op_name=$1
    local class_name=$(echo $op_name | sed 's/\b\w/\U&/g')  # Capitalize first letter
    
    cat > "${op_name}_riscv.cpp" << EOF
// Tencent is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#include "${op_name}_riscv.h"

#if __riscv_vector
#include <riscv_vector.h>
#endif // __riscv_vector

namespace ncnn {

int ${class_name}_riscv::forward_inplace(Mat& bottom_top_blob, const Option& opt) const
{
    int w = bottom_top_blob.w;
    int h = bottom_top_blob.h;
    int d = bottom_top_blob.d;
    int channels = bottom_top_blob.c;
    int elempack = bottom_top_blob.elempack;
    int size = w * h * d * elempack;

    #pragma omp parallel for num_threads(opt.num_threads)
    for (int q = 0; q < channels; q++)
    {
        float* ptr = bottom_top_blob.channel(q);

#if __riscv_vector
        int n = size;
        while (n > 0)
        {
            size_t vl = __riscv_vsetvl_e32m8(n);

            vfloat32m8_t _p = __riscv_vle32_v_f32m8(ptr, vl);
            
            // TODO: Implement ${op_name} operation
            vfloat32m8_t _result = _p; // Placeholder
            
            __riscv_vse32_v_f32m8(ptr, _result, vl);

            ptr += vl;
            n -= vl;
        }
#else  // __riscv_vector
        for (int i = 0; i < size; i++)
        {
            // TODO: Implement scalar fallback for ${op_name}
            ptr++;
        }
#endif // __riscv_vector
    }

    return 0;
}

} // namespace ncnn
EOF
}

# Create all operators
echo "Creating RISC-V operator templates..."

for op in "${OPERATORS[@]}"; do
    echo "Creating ${op}_riscv.h and ${op}_riscv.cpp"
    create_header "$op"
    create_implementation "$op"
done

echo "Done! Created ${#OPERATORS[@]} operator templates."
echo ""
echo "Note: These are template files. You need to implement the specific"
echo "vector operations for each operator based on their functionality."
echo ""
echo "To add them to the Makefile, run:"
echo "  ./update_makefile.sh"
