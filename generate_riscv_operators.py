#!/usr/bin/env python3
"""
Script to generate RISC-V versions of NCNN operators from x86 versions.
This script creates header and implementation files for RISC-V Vector intrinsics.
"""

import os
import re
from typing import List, Tuple

# List of operators to migrate (operator_name, has_parameters)
OPERATORS = [
    # Activation functions
    ("gelu", False),
    ("mish", False), 
    ("selu", False),
    ("hardsigmoid", False),
    ("hardswish", False),
    ("softmax", False),
    
    # Normalization
    ("layernorm", False),
    ("groupnorm", False),
    ("rmsnorm", False),
    
    # Basic operations
    ("clip", False),
    ("eltwise", False),
    ("unaryop", False),
    ("prelu", True),
    ("bnll", False),
    
    # Data operations
    ("packing", False),
    ("padding", False),
    ("crop", False),
    ("concat", False),
    ("slice", False),
    ("reshape", False),
    ("flatten", False),
    ("shufflechannel", False),
    
    # Pooling and interpolation
    ("pooling", True),
    ("interp", True),
    
    # Quantization
    ("quantize", False),
    ("dequantize", False),
    ("requantize", False),
    
    # Others
    ("lrn", False),
    ("dropout", False),
]

def generate_header(operator_name: str) -> str:
    """Generate header file content for RISC-V operator."""
    class_name = operator_name.capitalize()
    header_guard = f"LAYER_{operator_name.upper()}_RISCV_H"
    
    return f"""// Tencent is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#ifndef {header_guard}
#define {header_guard}

#include "{operator_name}.h"

namespace ncnn {{

class {class_name}_riscv : public {class_name}
{{
public:
    virtual int forward_inplace(Mat& bottom_top_blob, const Option& opt) const;
}};

}} // namespace ncnn

#endif // {header_guard}"""

def generate_simple_activation_cpp(operator_name: str, operation: str) -> str:
    """Generate implementation for simple activation functions."""
    class_name = operator_name.capitalize()
    
    return f"""// Tencent is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#include "{operator_name}_riscv.h"

#if __riscv_vector
#include <riscv_vector.h>
#endif // __riscv_vector

namespace ncnn {{

int {class_name}_riscv::forward_inplace(Mat& bottom_top_blob, const Option& opt) const
{{
    int w = bottom_top_blob.w;
    int h = bottom_top_blob.h;
    int d = bottom_top_blob.d;
    int channels = bottom_top_blob.c;
    int elempack = bottom_top_blob.elempack;
    int size = w * h * d * elempack;

    #pragma omp parallel for num_threads(opt.num_threads)
    for (int q = 0; q < channels; q++)
    {{
        float* ptr = bottom_top_blob.channel(q);

#if __riscv_vector
        int n = size;
        while (n > 0)
        {{
            size_t vl = __riscv_vsetvl_e32m8(n);

            vfloat32m8_t _p = __riscv_vle32_v_f32m8(ptr, vl);
            
            // {operation}
            vfloat32m8_t _result = _p; // TODO: Implement specific operation
            
            __riscv_vse32_v_f32m8(ptr, _result, vl);

            ptr += vl;
            n -= vl;
        }}
#else  // __riscv_vector
        for (int i = 0; i < size; i++)
        {{
            // TODO: Implement scalar fallback
            ptr++;
        }}
#endif // __riscv_vector
    }}

    return 0;
}}

}} // namespace ncnn"""

def generate_files():
    """Generate all RISC-V operator files."""
    for operator_name, has_params in OPERATORS:
        # Generate header file
        header_content = generate_header(operator_name)
        header_filename = f"{operator_name}_riscv.h"
        
        with open(header_filename, 'w') as f:
            f.write(header_content)
        
        print(f"Generated {header_filename}")
        
        # Generate implementation file
        if operator_name in ["gelu", "mish", "selu", "hardsigmoid", "hardswish"]:
            # Simple activation functions
            operation_map = {
                "gelu": "GELU activation: x * 0.5 * (1 + tanh(sqrt(2/π) * (x + 0.044715 * x³)))",
                "mish": "Mish activation: x * tanh(softplus(x))",
                "selu": "SELU activation: scale * (max(0,x) + min(0, alpha * (exp(x) - 1)))",
                "hardsigmoid": "Hard Sigmoid: max(0, min(1, (x + 3) / 6))",
                "hardswish": "Hard Swish: x * hardsigmoid(x)"
            }
            cpp_content = generate_simple_activation_cpp(operator_name, operation_map.get(operator_name, "TODO"))
        else:
            # Generic template
            cpp_content = generate_simple_activation_cpp(operator_name, "Generic operation")
        
        cpp_filename = f"{operator_name}_riscv.cpp"
        
        with open(cpp_filename, 'w') as f:
            f.write(cpp_content)
        
        print(f"Generated {cpp_filename}")

if __name__ == "__main__":
    generate_files()
    print("\\nAll RISC-V operator files generated!")
    print("Note: These are template files. You need to implement the specific")
    print("vector operations for each operator based on their functionality.")
